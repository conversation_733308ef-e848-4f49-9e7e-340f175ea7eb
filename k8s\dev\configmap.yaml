﻿apiVersion: v1
kind: ConfigMap
metadata:
  name: imip-wisma-config
  namespace: imip-wisma-dev-new
data:
  ASPNETCORE_ENVIRONMENT: "Development"
  App__SelfUrl: "${DEV_APP_URL}"
  App__ServerRootAddress: "${DEV_APP_URL}"
  App__ClientUrl: "${DEV_CLIENT_URL}"
  App__CorsOrigins: "${DEV_CORS_ORIGINS}"
  App__HealthCheckUrl: "/api/health/kubernetes"
  Seq__ServerUrl: "${SEQ_SERVER_URL}"
  AuthServer__Authority: "${DEV_AUTH_APP_URL}"
  AuthServer__RequireHttpsMetadata: "false"
  ExternalAuth__ApiUrl: "${DEV_EXTERNAL_AUTH_URL}"
  ExternalAuth__Enabled: "true"
  AuthServer__CertificatePath: "/app/certs/identity-server.pfx"
  Redis__IsEnabled: "true"
  DocumentConversion__MaxConcurrentConversions: "2"
  PdfOptimization__EmbedFonts: "false"
  PdfOptimization__OptimizeIdenticalImages: "true"
  PdfOptimization__CompressionLevel: "Best"
  PdfOptimization__MaxFileSizeWarningMB: "5.0"
  PdfOptimization__MaxFileSizeForPostProcessingMB: "2.0"
  PdfOptimization__UseMinimalFonts: "true"
  PdfOptimization__DefaultFontFamily: "Arial"
  PdfOptimization__UseLighterFontWeight: "true"
  Clock__Kind: "Local"
  Redis__Configuration: "**********:6379,abortConnect=false,connectTimeout=30000,syncTimeout=30000,connectRetry=10,keepAlive=60,allowAdmin=true,responseTimeout=30000"
  # User Synchronization Configuration for Development
  UserSynchronization__IsEnabled: "${DEV_USER_SYNC_ENABLED}"
  UserSynchronization__UpdateExistingUsers: "${DEV_USER_SYNC_UPDATE_EXISTING}"
  UserSynchronization__SynchronizeRoles: "${DEV_USER_SYNC_ROLES}"
  UserSynchronization__SynchronizeClaims: "${DEV_USER_SYNC_CLAIMS}"
  UserSynchronization__EnableLogging: "${DEV_USER_SYNC_LOGGING}"
  UserSynchronization__HandleUsernameConflictsGracefully: "${DEV_USER_SYNC_HANDLE_CONFLICTS_GRACEFULLY}"
  UserSynchronization__MakeFailuresNonBlocking: "${DEV_USER_SYNC_MAKE_FAILURES_NON_BLOCKING}"
