# User Synchronization Kubernetes Configuration Update

## Overview

This document describes the updates made to the Kubernetes configuration files to support the new user synchronization features for handling username conflicts gracefully and making failures non-blocking.

## Changes Made

### 1. ConfigMap Updates

#### Development Environment (`k8s/dev/configmap.yaml`)
Added new configuration options:
```yaml
UserSynchronization__HandleUsernameConflictsGracefully: "${DEV_USER_SYNC_HANDLE_CONFLICTS_GRACEFULLY}"
UserSynchronization__MakeFailuresNonBlocking: "${DEV_USER_SYNC_MAKE_FAILURES_NON_BLOCKING}"
```

#### Production Environment (`k8s/prod/configmap.yaml`)
Added new configuration options:
```yaml
UserSynchronization__HandleUsernameConflictsGracefully: "${PROD_USER_SYNC_HANDLE_CONFLICTS_GRACEFULLY}"
UserSynchronization__MakeFailuresNonBlocking: "${PROD_USER_SYNC_MAKE_FAILURES_NON_BLOCKING}"
```

### 2. GitLab CI/CD Pipeline Updates (`.gitlab-ci.yml`)

#### Environment Variable Definitions
Added new environment variables for both development and production:

**Development:**
```yaml
DEV_USER_SYNC_HANDLE_CONFLICTS_GRACEFULLY: "true"
DEV_USER_SYNC_MAKE_FAILURES_NON_BLOCKING: "true"
```

**Production:**
```yaml
PROD_USER_SYNC_HANDLE_CONFLICTS_GRACEFULLY: "true"
PROD_USER_SYNC_MAKE_FAILURES_NON_BLOCKING: "true"
```

#### Export Statements
Added export statements in the deployment stages:

**Development Deployment:**
```bash
- export DEV_USER_SYNC_HANDLE_CONFLICTS_GRACEFULLY="${DEV_USER_SYNC_HANDLE_CONFLICTS_GRACEFULLY}"
- export DEV_USER_SYNC_MAKE_FAILURES_NON_BLOCKING="${DEV_USER_SYNC_MAKE_FAILURES_NON_BLOCKING}"
```

**Production Deployment:**
```bash
- export PROD_USER_SYNC_HANDLE_CONFLICTS_GRACEFULLY="${PROD_USER_SYNC_HANDLE_CONFLICTS_GRACEFULLY}"
- export PROD_USER_SYNC_MAKE_FAILURES_NON_BLOCKING="${PROD_USER_SYNC_MAKE_FAILURES_NON_BLOCKING}"
```

## Configuration Options

### HandleUsernameConflictsGracefully
- **Purpose**: When enabled, the system will handle username conflicts by updating existing users instead of failing
- **Default Value**: `true` for both environments
- **Behavior**: 
  - If a user with the same username exists but different ID, update the existing user
  - Logs a warning about the conflict but continues processing
  - Prevents `DuplicateUserName` errors

### MakeFailuresNonBlocking
- **Purpose**: When enabled, user synchronization failures won't block the request pipeline
- **Default Value**: `true` for both environments
- **Behavior**:
  - If user synchronization fails, the system continues processing
  - Attempts to find existing users as fallback
  - Logs warnings but doesn't throw exceptions

## Environment-Specific Settings

### Development Environment
- **HandleUsernameConflictsGracefully**: `true` - Enables graceful conflict handling for easier development
- **MakeFailuresNonBlocking**: `true` - Ensures development workflow isn't blocked by sync issues
- **EnableLogging**: `true` - Full logging for debugging

### Production Environment
- **HandleUsernameConflictsGracefully**: `true` - Ensures production stability
- **MakeFailuresNonBlocking**: `true` - Prevents production outages due to sync issues
- **EnableLogging**: `false` - Reduced logging for performance

## Deployment Process

1. **ConfigMap Application**: The new configuration options are automatically applied when the ConfigMap is updated
2. **Environment Variable Substitution**: GitLab CI/CD substitutes the environment variables during deployment
3. **Application Restart**: The application will pick up the new configuration on the next deployment

## Verification

After deployment, you can verify the configuration is working by:

1. **Checking ConfigMap**:
   ```bash
   kubectl get configmap imip-wisma-config -n imip-wisma-dev-new -o yaml
   kubectl get configmap imip-wisma-config -n imip-wisma-prod -o yaml
   ```

2. **Checking Application Logs**:
   ```bash
   kubectl logs -n imip-wisma-dev-new deployment/imip-wisma-web | grep "UserSynchronization"
   kubectl logs -n imip-wisma-prod deployment/imip-wisma-web | grep "UserSynchronization"
   ```

3. **Testing Username Conflicts**: Attempt to access the system with a user that has a username conflict to verify graceful handling

## Benefits

1. **Improved Stability**: Username conflicts no longer cause application failures
2. **Better User Experience**: Users can continue using the system even if synchronization has issues
3. **Easier Debugging**: Better logging and error handling for troubleshooting
4. **Production Reliability**: Non-blocking failures prevent production outages

## Rollback Plan

If issues arise, you can quickly rollback by:

1. **Disabling Conflict Handling**:
   ```yaml
   UserSynchronization__HandleUsernameConflictsGracefully: "false"
   ```

2. **Making Failures Blocking**:
   ```yaml
   UserSynchronization__MakeFailuresNonBlocking: "false"
   ```

3. **Redeploy**: The changes will take effect on the next deployment

## Monitoring

Monitor the following metrics after deployment:

1. **User Synchronization Success Rate**: Should improve with graceful conflict handling
2. **Application Error Rate**: Should decrease with non-blocking failures
3. **User Login Success Rate**: Should remain stable or improve
4. **System Performance**: Should improve with reduced error handling overhead 