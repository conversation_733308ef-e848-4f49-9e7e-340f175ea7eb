using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.IdentityModel.Tokens.Jwt;
using System.Linq;
using System.Security.Claims;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Volo.Abp;
using Volo.Abp.Application.Services;
using Volo.Abp.AspNetCore.Mvc;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.Identity;
using Volo.Abp.Uow;
using IdentityUser = Volo.Abp.Identity.IdentityUser;

namespace Imip.HotelFrontOffice.Users;

/// <summary>
/// Service for synchronizing users between Identity Server and internal database
/// </summary>
[RemoteService(false)] // Exclude this service from remote service exposure
public class UserSynchronizationService : ApplicationService, IUserSynchronizationService
{
    private readonly IIdentityUserRepository _userRepository;
    private readonly IdentityUserManager _userManager;
    private readonly IIdentityRoleRepository _roleRepository;
    private readonly ILogger<UserSynchronizationService> _logger;
    private readonly IOptions<UserSynchronizationOptions> _options;
    private readonly IMemoryCache _cache;

    // Cache keys and settings
    private const string USER_CACHE_KEY_PREFIX = "UserSync:User:";
    private const string USER_HASH_CACHE_KEY_PREFIX = "UserSync:Hash:";
    private static readonly TimeSpan CacheExpiration = TimeSpan.FromMinutes(30);

    // In-memory cache for frequently accessed users (per request)
    private static readonly ConcurrentDictionary<Guid, IdentityUser> _requestCache = new();

    public UserSynchronizationService(
        IIdentityUserRepository userRepository,
        IdentityUserManager userManager,
        IIdentityRoleRepository roleRepository,
        ILogger<UserSynchronizationService> logger,
        IOptions<UserSynchronizationOptions> options,
        IMemoryCache cache)
    {
        _userRepository = userRepository;
        _userManager = userManager;
        _roleRepository = roleRepository;
        _logger = logger;
        _options = options;
        _cache = cache;
    }

    /// <summary>
    /// Synchronizes user from JWT token claims to internal database (with caching)
    /// </summary>
    [NonAction] // Prevent ABP from auto-exposing this as an API endpoint
    public async Task<IdentityUser> SynchronizeUserFromTokenAsync(string token)
    {
        if (string.IsNullOrEmpty(token))
        {
            throw new ArgumentException("Token cannot be null or empty", nameof(token));
        }

        try
        {
            var jwtHandler = new JwtSecurityTokenHandler();

            if (!jwtHandler.CanReadToken(token))
            {
                throw new BusinessException("Invalid JWT token format");
            }

            var jwtToken = jwtHandler.ReadJwtToken(token);

            // Fast path: Extract only the user ID first to check cache
            var userIdClaim = jwtToken.Claims.FirstOrDefault(c =>
                c.Type == "sub" || c.Type == ClaimTypes.NameIdentifier || c.Type == "user_id");

            if (userIdClaim != null && Guid.TryParse(userIdClaim.Value, out var userId))
            {
                // Check request cache first (fastest)
                if (_requestCache.TryGetValue(userId, out var cachedUser))
                {
                    return cachedUser;
                }

                // Check memory cache
                var cacheKey = USER_CACHE_KEY_PREFIX + userId;
                if (_cache.TryGetValue(cacheKey, out IdentityUser? memCachedUser) && memCachedUser != null)
                {
                    _requestCache.TryAdd(userId, memCachedUser);
                    return memCachedUser;
                }

                // Check if user exists in database (lightweight check)
                var existingUser = await _userRepository.FindAsync(userId);
                if (existingUser != null)
                {
                    // Cache the user
                    _cache.Set(cacheKey, existingUser, new MemoryCacheEntryOptions
                    {
                        AbsoluteExpirationRelativeToNow = CacheExpiration,
                        Size = 2 // User objects are larger, assign more units
                    });
                    _requestCache.TryAdd(userId, existingUser);

                    return existingUser;
                }
            }

            // User doesn't exist, proceed with full synchronization
            return await SynchronizeUserFromClaimsAsync(jwtToken.Claims);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error synchronizing user from token");
            throw new BusinessException("Failed to synchronize user from token");
        }
    }

    /// <summary>
    /// Synchronizes user from claims principal to internal database
    /// </summary>
    [NonAction] // Prevent ABP from auto-exposing this as an API endpoint
    public async Task<IdentityUser> SynchronizeUserFromClaimsAsync(ClaimsPrincipal claimsPrincipal)
    {
        if (claimsPrincipal?.Identity?.IsAuthenticated != true)
        {
            throw new BusinessException("Claims principal is not authenticated");
        }

        return await SynchronizeUserFromClaimsAsync(claimsPrincipal.Claims);
    }

    /// <summary>
    /// Synchronizes user from claims collection to internal database
    /// </summary>
    [NonAction] // Prevent ABP from auto-exposing this as an API endpoint
    public async Task<IdentityUser> SynchronizeUserFromClaimsAsync(IEnumerable<Claim> claims)
    {
        var claimsList = claims.ToList();
        var userInfo = ExtractUserInfoFromClaims(claimsList);

        if (userInfo.Id == Guid.Empty)
        {
            throw new BusinessException("User ID not found in token claims");
        }

        // Validate that we have a username after extraction and sanitization
        if (string.IsNullOrEmpty(userInfo.UserName))
        {
            throw new BusinessException("Username could not be determined from token claims");
        }

        var result = await CreateOrUpdateUserAsync(userInfo);

        // Handle the case where CreateOrUpdateUserAsync returns null due to non-blocking configuration
        if (result == null)
        {
            _logger.LogWarning("User synchronization failed for {UserId} ({UserName}) but continuing due to non-blocking configuration",
                              userInfo.Id, userInfo.UserName);

            // Try to find an existing user by username as a fallback
            var existingUser = await _userManager.FindByNameAsync(userInfo.UserName);
            if (existingUser != null)
            {
                _logger.LogInformation("Using existing user {UserId} ({UserName}) as fallback",
                                      existingUser.Id, existingUser.UserName);
                return existingUser;
            }

            // If no existing user found, throw an exception
            throw new BusinessException($"User synchronization failed for {userInfo.UserName} and no existing user found");
        }

        return result;
    }

    /// <summary>
    /// Checks if user exists in internal database
    /// </summary>
    [NonAction] // Prevent ABP from auto-exposing this as an API endpoint
    public async Task<bool> UserExistsAsync(Guid userId)
    {
        return await _userRepository.FindAsync(userId) != null;
    }

    /// <summary>
    /// Gets user from internal database by ID
    /// </summary>
    [NonAction] // Prevent ABP from auto-exposing this as an API endpoint
    public async Task<IdentityUser?> GetUserAsync(Guid userId)
    {
        return await _userRepository.FindAsync(userId);
    }

    /// <summary>
    /// Creates or updates user in internal database (with caching)
    /// </summary>
    [NonAction] // Prevent ABP from auto-exposing this as an API endpoint
    [UnitOfWork(isTransactional: true)]
    public async Task<IdentityUser> CreateOrUpdateUserAsync(UserSynchronizationInfo userInfo)
    {
        try
        {
            // Check cache first
            var cacheKey = USER_CACHE_KEY_PREFIX + userInfo.Id;

            // Check request cache
            if (_requestCache.TryGetValue(userInfo.Id, out var cachedUser))
            {
                var updatedUser = await UpdateUserAsync(cachedUser, userInfo);

                // Update caches
                _cache.Set(cacheKey, updatedUser, new MemoryCacheEntryOptions
                {
                    AbsoluteExpirationRelativeToNow = CacheExpiration,
                    Size = 2 // User objects are larger, assign more units
                });
                _requestCache.TryUpdate(userInfo.Id, updatedUser, cachedUser);

                return updatedUser;
            }

            // Check memory cache
            if (_cache.TryGetValue(cacheKey, out IdentityUser? memCachedUser) && memCachedUser != null)
            {
                var updatedUser = await UpdateUserAsync(memCachedUser, userInfo);

                // Update caches
                _cache.Set(cacheKey, updatedUser, new MemoryCacheEntryOptions
                {
                    AbsoluteExpirationRelativeToNow = CacheExpiration,
                    Size = 2 // User objects are larger, assign more units
                });
                _requestCache.TryAdd(userInfo.Id, updatedUser);

                return updatedUser;
            }

            // Check database by ID first
            var existingUser = await _userRepository.FindAsync(userInfo.Id);

            if (existingUser != null)
            {
                var updatedUser = await UpdateUserAsync(existingUser, userInfo);

                // Cache the updated user
                _cache.Set(cacheKey, updatedUser, new MemoryCacheEntryOptions
                {
                    AbsoluteExpirationRelativeToNow = CacheExpiration,
                    Size = 2 // User objects are larger, assign more units
                });
                _requestCache.TryAdd(userInfo.Id, updatedUser);

                return updatedUser;
            }
            else
            {
                // Check if a user with the same username already exists
                var existingUserByUsername = await _userManager.FindByNameAsync(userInfo.UserName);

                if (existingUserByUsername != null)
                {
                    if (_options.Value.HandleUsernameConflictsGracefully)
                    {
                        _logger.LogWarning("User with username '{UserName}' already exists with ID {ExistingUserId}, but current user has ID {CurrentUserId}. " +
                                          "Handling conflict gracefully by updating the existing user.",
                                          userInfo.UserName, existingUserByUsername.Id, userInfo.Id);

                        // Update the existing user with the new information
                        var updatedUser = await UpdateUserAsync(existingUserByUsername, userInfo);

                        // Cache the updated user with the current user's ID
                        _cache.Set(cacheKey, updatedUser, new MemoryCacheEntryOptions
                        {
                            AbsoluteExpirationRelativeToNow = CacheExpiration,
                            Size = 2
                        });
                        _requestCache.TryAdd(userInfo.Id, updatedUser);

                        return updatedUser;
                    }
                    else
                    {
                        _logger.LogError("User with username '{UserName}' already exists with ID {ExistingUserId}, but current user has ID {CurrentUserId}. " +
                                        "Username conflict handling is disabled. Cannot create new user.",
                                        userInfo.UserName, existingUserByUsername.Id, userInfo.Id);

                        throw new BusinessException($"Username '{userInfo.UserName}' is already taken by another user (ID: {existingUserByUsername.Id})");
                    }
                }

                // No conflicts, create new user
                var newUser = await CreateUserAsync(userInfo);

                // Cache the new user
                _cache.Set(cacheKey, newUser, new MemoryCacheEntryOptions
                {
                    AbsoluteExpirationRelativeToNow = CacheExpiration,
                    Size = 2 // User objects are larger, assign more units
                });
                _requestCache.TryAdd(userInfo.Id, newUser);

                return newUser;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating or updating user {UserId} ({UserName})", userInfo.Id, userInfo.UserName);

            // If failures should be non-blocking, return null instead of throwing
            if (_options.Value.MakeFailuresNonBlocking)
            {
                _logger.LogWarning("User synchronization failed for {UserId} ({UserName}), but continuing due to non-blocking configuration",
                                  userInfo.Id, userInfo.UserName);
                return null!;
            }

            throw;
        }
    }

    /// <summary>
    /// Extracts user information from JWT claims
    /// </summary>
    private UserSynchronizationInfo ExtractUserInfoFromClaims(IList<Claim> claims)
    {
        var userInfo = new UserSynchronizationInfo();

        foreach (var claim in claims)
        {
            switch (claim.Type)
            {
                case "sub":
                case ClaimTypes.NameIdentifier:
                case "user_id":
                    if (Guid.TryParse(claim.Value, out var userId))
                    {
                        userInfo.Id = userId;
                    }
                    break;

                case "preferred_username":
                case "unique_name":
                case "username":
                    // These claims contain the actual username (employee ID)
                    userInfo.UserName = claim.Value;
                    break;

                case ClaimTypes.Email:
                case "email":
                    userInfo.Email = claim.Value;
                    break;

                case ClaimTypes.GivenName:
                case "given_name":
                case "name":
                    userInfo.Name = claim.Value;
                    break;

                case ClaimTypes.Name:
                    // ClaimTypes.Name contains the full name, not username
                    // Store as display name, but don't use as username if we already have one
                    userInfo.Name = claim.Value;

                    // Only use as username if we don't have a username yet (fallback)
                    if (string.IsNullOrEmpty(userInfo.UserName))
                    {
                        userInfo.UserName = claim.Value;
                    }
                    break;

                case ClaimTypes.Surname:
                case "family_name":
                case "surname":
                    userInfo.Surname = claim.Value;
                    break;

                case ClaimTypes.MobilePhone:
                case "phone_number":
                    userInfo.PhoneNumber = claim.Value;
                    break;

                case "email_verified":
                    if (bool.TryParse(claim.Value, out var emailVerified))
                    {
                        userInfo.EmailConfirmed = emailVerified;
                    }
                    break;

                case "phone_number_verified":
                    if (bool.TryParse(claim.Value, out var phoneVerified))
                    {
                        userInfo.PhoneNumberConfirmed = phoneVerified;
                    }
                    break;

                case ClaimTypes.Role:
                case "role":
                    if (!string.IsNullOrEmpty(claim.Value))
                    {
                        userInfo.Roles.Add(claim.Value);
                    }
                    break;

                case "tenant_id":
                    if (Guid.TryParse(claim.Value, out var tenantId))
                    {
                        userInfo.TenantId = tenantId;
                    }
                    break;

                default:
                    // Store additional claims
                    if (!string.IsNullOrEmpty(claim.Value))
                    {
                        userInfo.Claims[claim.Type] = claim.Value;
                    }
                    break;
            }
        }

        // Fallback for username if not found
        if (string.IsNullOrEmpty(userInfo.UserName))
        {
            userInfo.UserName = userInfo.Email ?? $"user_{userInfo.Id}";
            _logger.LogWarning("No username found in claims, using fallback: {UserName}", userInfo.UserName);
        }

        // Sanitize username to ensure it meets validation requirements
        userInfo.UserName = SanitizeUsername(userInfo.UserName);

        return userInfo;
    }

    /// <summary>
    /// Sanitizes username to ensure it meets Identity validation requirements
    /// </summary>
    private string SanitizeUsername(string username)
    {
        if (string.IsNullOrEmpty(username))
        {
            return username;
        }

        // Define allowed characters (same as configured in IdentityOptions)
        const string allowedChars = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789-._@+ ";

        // Remove any characters not in the allowed set
        var sanitized = new string(username.Where(c => allowedChars.Contains(c)).ToArray());

        // Ensure the username is not empty after sanitization
        if (string.IsNullOrEmpty(sanitized))
        {
            sanitized = $"user_{Guid.NewGuid():N}";
        }

        // Trim whitespace from start and end
        sanitized = sanitized.Trim();

        // If still empty after trimming, generate a fallback
        if (string.IsNullOrEmpty(sanitized))
        {
            sanitized = $"user_{Guid.NewGuid():N}";
        }

        return sanitized;
    }

    /// <summary>
    /// Invalidates user cache
    /// </summary>
    private void InvalidateUserCache(Guid userId)
    {
        var cacheKey = USER_CACHE_KEY_PREFIX + userId;
        _cache.Remove(cacheKey);
        _requestCache.TryRemove(userId, out _);
    }

    /// <summary>
    /// Clears all user caches (useful for testing or maintenance)
    /// </summary>
    [NonAction]
    public void ClearAllCaches()
    {
        _requestCache.Clear();
        _logger.LogInformation("Cleared all user caches");
    }

    /// <summary>
    /// Creates a new user in the internal database
    /// </summary>
    private async Task<IdentityUser> CreateUserAsync(UserSynchronizationInfo userInfo)
    {
        var user = new IdentityUser(
            id: userInfo.Id,
            userName: userInfo.UserName,
            email: userInfo.Email,
            tenantId: userInfo.TenantId)
        {
            Name = userInfo.Name,
            Surname = userInfo.Surname
        };

        // Set a random password since this user is authenticated externally
        var result = await _userManager.CreateAsync(user, Guid.NewGuid().ToString("N")[..16] + "Aa1!");

        if (!result.Succeeded)
        {
            var errors = string.Join(", ", result.Errors.Select(e => $"{e.Code}: {e.Description}"));
            _logger.LogError("Failed to create user {UserId} ({UserName}). Validation errors: {Errors}",
                userInfo.Id, userInfo.UserName, errors);

            // Log individual errors for debugging
            foreach (var error in result.Errors)
            {
                _logger.LogError("Identity validation error - Code: {Code}, Description: {Description}",
                    error.Code, error.Description);
            }

            // Check if this is a username conflict and provide a more specific error message
            if (result.Errors.Any(e => e.Code == "DuplicateUserName"))
            {
                if (_options.Value.HandleUsernameConflictsGracefully)
                {
                    _logger.LogWarning("Username conflict detected for '{UserName}'. This should have been handled by the calling method. " +
                                      "Attempting to find and update existing user.", userInfo.UserName);

                    // Try to find the existing user by username
                    var existingUser = await _userManager.FindByNameAsync(userInfo.UserName);
                    if (existingUser != null)
                    {
                        _logger.LogInformation("Found existing user with username '{UserName}' and ID {ExistingUserId}. " +
                                              "Updating instead of creating new user.", userInfo.UserName, existingUser.Id);

                        // Update the existing user instead
                        return await UpdateUserAsync(existingUser, userInfo);
                    }
                }
                else
                {
                    _logger.LogError("Username conflict detected for '{UserName}' but graceful handling is disabled.", userInfo.UserName);
                }
            }

            throw new BusinessException($"Failed to create user: {errors}");
        }

        // Set properties that require special handling
        if (!string.IsNullOrEmpty(userInfo.PhoneNumber))
        {
            await _userManager.SetPhoneNumberAsync(user, userInfo.PhoneNumber);
        }

        // Set email and phone confirmation status using reflection or direct property access
        if (userInfo.EmailConfirmed)
        {
            var token = await _userManager.GenerateEmailConfirmationTokenAsync(user);
            await _userManager.ConfirmEmailAsync(user, token);
        }

        if (userInfo.PhoneNumberConfirmed && !string.IsNullOrEmpty(userInfo.PhoneNumber))
        {
            var token = await _userManager.GenerateChangePhoneNumberTokenAsync(user, userInfo.PhoneNumber);
            await _userManager.ChangePhoneNumberAsync(user, userInfo.PhoneNumber, token);
        }

        // Set active status
        await _userManager.SetLockoutEnabledAsync(user, !userInfo.IsActive);
        if (!userInfo.IsActive)
        {
            await _userManager.SetLockoutEndDateAsync(user, DateTimeOffset.MaxValue);
        }

        // Add roles if any
        await AssignRolesToUserAsync(user, userInfo.Roles);

        // Add additional claims
        await AddClaimsToUserAsync(user, userInfo.Claims);

        return user;
    }

    /// <summary>
    /// Updates an existing user in the internal database
    /// </summary>
    private async Task<IdentityUser> UpdateUserAsync(IdentityUser existingUser, UserSynchronizationInfo userInfo)
    {
        var hasChanges = false;

        // Update basic properties
        if (existingUser.UserName != userInfo.UserName)
        {
            await _userManager.SetUserNameAsync(existingUser, userInfo.UserName);
            hasChanges = true;
        }

        if (existingUser.Email != userInfo.Email && !string.IsNullOrEmpty(userInfo.Email))
        {
            await _userManager.SetEmailAsync(existingUser, userInfo.Email);
            hasChanges = true;
        }

        if (existingUser.PhoneNumber != userInfo.PhoneNumber && !string.IsNullOrEmpty(userInfo.PhoneNumber))
        {
            await _userManager.SetPhoneNumberAsync(existingUser, userInfo.PhoneNumber);
            hasChanges = true;
        }

        if (existingUser.Name != userInfo.Name)
        {
            existingUser.Name = userInfo.Name;
            hasChanges = true;
        }

        if (existingUser.Surname != userInfo.Surname)
        {
            existingUser.Surname = userInfo.Surname;
            hasChanges = true;
        }

        // Handle email confirmation status
        if (existingUser.EmailConfirmed != userInfo.EmailConfirmed)
        {
            if (userInfo.EmailConfirmed && !existingUser.EmailConfirmed)
            {
                var token = await _userManager.GenerateEmailConfirmationTokenAsync(existingUser);
                await _userManager.ConfirmEmailAsync(existingUser, token);
                hasChanges = true;
            }
        }

        // Handle phone confirmation status
        if (existingUser.PhoneNumberConfirmed != userInfo.PhoneNumberConfirmed)
        {
            if (userInfo.PhoneNumberConfirmed && !existingUser.PhoneNumberConfirmed && !string.IsNullOrEmpty(userInfo.PhoneNumber))
            {
                var token = await _userManager.GenerateChangePhoneNumberTokenAsync(existingUser, userInfo.PhoneNumber);
                await _userManager.ChangePhoneNumberAsync(existingUser, userInfo.PhoneNumber, token);
                hasChanges = true;
            }
        }

        // Handle active status
        if (existingUser.IsActive != userInfo.IsActive)
        {
            await _userManager.SetLockoutEnabledAsync(existingUser, !userInfo.IsActive);
            if (!userInfo.IsActive)
            {
                await _userManager.SetLockoutEndDateAsync(existingUser, DateTimeOffset.MaxValue);
            }
            else
            {
                await _userManager.SetLockoutEndDateAsync(existingUser, null);
            }
            hasChanges = true;
        }

        if (hasChanges)
        {
            await _userManager.UpdateAsync(existingUser);
        }

        // Update roles
        await SynchronizeUserRolesAsync(existingUser, userInfo.Roles);

        // Update claims
        await SynchronizeUserClaimsAsync(existingUser, userInfo.Claims);

        return existingUser;
    }

    /// <summary>
    /// Assigns roles to user
    /// </summary>
    private async Task AssignRolesToUserAsync(IdentityUser user, List<string> roles)
    {
        if (roles.Count == 0) return;

        foreach (var roleName in roles)
        {
            try
            {
                var role = await _roleRepository.FindByNormalizedNameAsync(roleName.ToUpperInvariant());
                if (role != null)
                {
                    await _userManager.AddToRoleAsync(user, roleName);
                }
                else
                {
                    _logger.LogWarning("Role {RoleName} not found in internal database", roleName);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error adding role {RoleName} to user {UserId}", roleName, user.Id);
            }
        }
    }

    /// <summary>
    /// Adds claims to user
    /// </summary>
    private async Task AddClaimsToUserAsync(IdentityUser user, Dictionary<string, string> claims)
    {
        if (claims.Count == 0) return;

        var claimsToAdd = claims.Select(kvp => new Claim(kvp.Key, kvp.Value)).ToList();

        try
        {
            await _userManager.AddClaimsAsync(user, claimsToAdd);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error adding claims to user {UserId}", user.Id);
        }
    }

    /// <summary>
    /// Synchronizes user roles with Identity Server roles
    /// </summary>
    private async Task SynchronizeUserRolesAsync(IdentityUser user, List<string> newRoles)
    {
        try
        {
            var currentRoles = await _userManager.GetRolesAsync(user);
            var rolesToAdd = newRoles.Except(currentRoles).ToList();
            var rolesToRemove = currentRoles.Except(newRoles).ToList();

            // Remove roles that are no longer present
            if (rolesToRemove.Count > 0)
            {
                await _userManager.RemoveFromRolesAsync(user, rolesToRemove);
            }

            // Add new roles
            if (rolesToAdd.Count > 0)
            {
                await AssignRolesToUserAsync(user, rolesToAdd);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error synchronizing roles for user {UserId}", user.Id);
        }
    }

    /// <summary>
    /// Synchronizes user claims with Identity Server claims
    /// </summary>
    private async Task SynchronizeUserClaimsAsync(IdentityUser user, Dictionary<string, string> newClaims)
    {
        if (newClaims.Count == 0) return;

        try
        {
            var currentClaims = await _userManager.GetClaimsAsync(user);
            var currentClaimsDict = currentClaims.ToDictionary(c => c.Type, c => c.Value);

            var claimsToAdd = new List<Claim>();
            var claimsToRemove = new List<Claim>();

            // Find claims to add or update
            foreach (var newClaim in newClaims)
            {
                if (!currentClaimsDict.ContainsKey(newClaim.Key))
                {
                    claimsToAdd.Add(new Claim(newClaim.Key, newClaim.Value));
                }
                else if (currentClaimsDict[newClaim.Key] != newClaim.Value)
                {
                    // Remove old claim and add new one
                    claimsToRemove.Add(new Claim(newClaim.Key, currentClaimsDict[newClaim.Key]));
                    claimsToAdd.Add(new Claim(newClaim.Key, newClaim.Value));
                }
            }

            // Remove outdated claims
            if (claimsToRemove.Count > 0)
            {
                await _userManager.RemoveClaimsAsync(user, claimsToRemove);
            }

            // Add new claims
            if (claimsToAdd.Count > 0)
            {
                await _userManager.AddClaimsAsync(user, claimsToAdd);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error synchronizing claims for user {UserId}", user.Id);
        }
    }

    /// <summary>
    /// Gets health check information for user synchronization
    /// </summary>
    [NonAction] // Prevent ABP from auto-exposing this as an API endpoint
    public async Task<UserSynchronizationHealthDto> GetHealthAsync()
    {
        try
        {
            var options = _options.Value;
            var totalUsers = await _userRepository.GetCountAsync();

            return new UserSynchronizationHealthDto
            {
                IsEnabled = options.IsEnabled,
                IsHealthy = true,
                Configuration = new UserSynchronizationConfigDto
                {
                    IsEnabled = options.IsEnabled,
                    UpdateExistingUsers = options.UpdateExistingUsers,
                    SynchronizeRoles = options.SynchronizeRoles,
                    SynchronizeClaims = options.SynchronizeClaims,
                    EnableLogging = options.EnableLogging
                },
                TotalUsersSynchronized = (int)totalUsers,
                CheckTimestamp = DateTime.UtcNow
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting user synchronization health status");

            return new UserSynchronizationHealthDto
            {
                IsEnabled = false,
                IsHealthy = false,
                Configuration = new UserSynchronizationConfigDto(),
                ErrorMessage = ex.Message,
                CheckTimestamp = DateTime.UtcNow
            };
        }
    }

    /// <summary>
    /// Handles username conflicts by finding existing users with the same username
    /// </summary>
    private async Task<IdentityUser?> HandleUsernameConflictAsync(string username, Guid currentUserId)
    {
        try
        {
            var existingUser = await _userManager.FindByNameAsync(username);

            if (existingUser != null && existingUser.Id != currentUserId)
            {
                _logger.LogWarning("Username conflict detected: User '{UserName}' exists with ID {ExistingUserId}, " +
                                  "but current request is for ID {CurrentUserId}. This may indicate a data synchronization issue.",
                                  username, existingUser.Id, currentUserId);

                return existingUser;
            }

            return null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking for username conflict for '{UserName}'", username);
            return null;
        }
    }
}
